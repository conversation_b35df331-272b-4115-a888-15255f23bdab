#!/usr/bin/env python3
"""Test script to verify PySwarms optimizer changes."""

import sys
import os

# Add the optimagic source to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'optimagic', 'src'))

try:
    import numpy as np
    from optimagic.optimizers.pyswarms_optimizers import (
        _create_objective_wrapper,
        _process_pyswarms_result,
        _create_initial_population,
        _convert_bounds_to_pyswarms
    )
    from optimagic.optimization.internal_optimization_problem import InternalOptimizationProblem
    from optimagic.optimization.internal_optimization_problem import InternalBounds
    
    print("✓ Successfully imported all functions")
    
    # Test objective wrapper
    class MockProblem:
        def fun(self, x):
            return np.sum(x**2)
    
    problem = MockProblem()
    wrapper = _create_objective_wrapper(problem)
    
    # Test with 2D input (as PySwarms provides)
    test_input = np.array([[1.0, 2.0], [3.0, 4.0], [5.0, 6.0]])
    result = wrapper(test_input)
    expected = np.array([5.0, 25.0, 61.0])  # [1^2+2^2, 3^2+4^2, 5^2+6^2]
    
    assert np.allclose(result, expected), f"Expected {expected}, got {result}"
    print("✓ Objective wrapper test passed")
    
    # Test result processing
    mock_result = (10.5, np.array([1.0, 2.0]))
    processed = _process_pyswarms_result(mock_result, n_particles=20, n_iterations_run=100)
    
    assert processed.x.tolist() == [1.0, 2.0]
    assert processed.fun == 10.5
    assert processed.n_fun_evals == 2000  # 20 * 100
    assert processed.n_jac_evals == 0
    assert processed.n_iterations == 100
    print("✓ Result processing test passed")
    
    # Test initial population creation
    x0 = np.array([1.0, 2.0])
    n_particles = 10
    bounds = (np.array([0.0, 0.0]), np.array([5.0, 5.0]))
    
    init_pop = _create_initial_population(x0, n_particles, bounds, center=1.0)
    
    assert init_pop.shape == (10, 2)
    assert np.allclose(init_pop[0], x0), f"First particle should be x0, got {init_pop[0]}"
    assert np.all(init_pop >= 0.0) and np.all(init_pop <= 5.0), "All particles should be within bounds"
    print("✓ Initial population test passed")
    
    # Test without bounds
    init_pop_no_bounds = _create_initial_population(x0, n_particles, None, center=2.0)
    assert init_pop_no_bounds.shape == (10, 2)
    assert np.allclose(init_pop_no_bounds[0], x0)
    print("✓ Initial population without bounds test passed")
    
    print("\n🎉 All tests passed successfully!")
    
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("This is expected if numpy or other dependencies are not installed")
except Exception as e:
    print(f"❌ Test failed: {e}")
    import traceback
    traceback.print_exc()
